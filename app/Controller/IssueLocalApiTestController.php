<?php

declare(strict_types=1);

namespace App\Controller;

use App\Core\Services\Project\IssueLocalApiService;
use App\Core\Utils\Log;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\Controller;
use Hyperf\HttpServer\Annotation\RequestMapping;
use Hyperf\HttpServer\Contract\RequestInterface;
use Hyperf\HttpServer\Contract\ResponseInterface;

/**
 * Issue本地API测试控制器
 * 
 * @Controller(prefix="/api/test/issue-local-api")
 */
class IssueLocalApiTestController extends AbstractController
{
    /**
     * @Inject()
     * @var IssueLocalApiService
     */
    protected $issueLocalApiService;

    /**
     * 测试创建Issue
     * 
     * @RequestMapping(path="/create", methods="POST")
     */
    public function testCreate(RequestInterface $request, ResponseInterface $response)
    {
        try {
            $params = [
                'project_id' => $request->input('project_id', 1),
                'tracker_id' => $request->input('tracker_id', 1),
                'subject' => $request->input('subject', '测试Issue - 本地API创建 ' . date('Y-m-d H:i:s')),
                'description' => $request->input('description', '这是通过本地API创建的测试Issue，创建时间：' . date('Y-m-d H:i:s')),
                'priority_id' => $request->input('priority_id', 3),
                'status_id' => $request->input('status_id', 1),
                'start_date' => $request->input('start_date', date('Y-m-d')),
                'due_date' => $request->input('due_date', date('Y-m-d', strtotime('+7 days'))),
                'estimated_hours' => $request->input('estimated_hours', 8.0),
                'done_ratio' => $request->input('done_ratio', 0)
            ];

            $result = $this->issueLocalApiService->createIssue($params);
            
            return $this->success($result, '创建Issue成功');
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('测试创建Issue失败', [
                'params' => $request->all(),
                'error' => $e->getMessage()
            ]);
            
            return $this->error($e->getMessage());
        }
    }

    /**
     * 测试更新Issue
     * 
     * @RequestMapping(path="/update/{id}", methods="PUT")
     */
    public function testUpdate(int $id, RequestInterface $request, ResponseInterface $response)
    {
        try {
            $params = [
                'subject' => $request->input('subject', '测试Issue - 本地API更新 ' . date('Y-m-d H:i:s')),
                'description' => $request->input('description', '这是通过本地API更新的测试Issue，更新时间：' . date('Y-m-d H:i:s')),
                'done_ratio' => $request->input('done_ratio', 50),
                'notes' => $request->input('notes', '通过本地API进行的更新测试 - ' . date('Y-m-d H:i:s'))
            ];

            // 过滤空值
            $params = array_filter($params, function($value) {
                return $value !== null && $value !== '';
            });

            $result = $this->issueLocalApiService->updateIssue($id, $params);
            
            return $this->success($result, '更新Issue成功');
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('测试更新Issue失败', [
                'issue_id' => $id,
                'params' => $request->all(),
                'error' => $e->getMessage()
            ]);
            
            return $this->error($e->getMessage());
        }
    }

    /**
     * 测试获取Issue详情
     * 
     * @RequestMapping(path="/get/{id}", methods="GET")
     */
    public function testGet(int $id, RequestInterface $request, ResponseInterface $response)
    {
        try {
            $result = $this->issueLocalApiService->getIssue($id);
            
            return $this->success($result, '获取Issue详情成功');
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('测试获取Issue详情失败', [
                'issue_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return $this->error($e->getMessage());
        }
    }

    /**
     * 测试删除Issue
     * 
     * @RequestMapping(path="/delete/{id}", methods="DELETE")
     */
    public function testDelete(int $id, RequestInterface $request, ResponseInterface $response)
    {
        try {
            $result = $this->issueLocalApiService->deleteIssue($id);
            
            return $this->success($result, '删除Issue成功');
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('测试删除Issue失败', [
                'issue_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            return $this->error($e->getMessage());
        }
    }

    /**
     * 测试添加关注人
     * 
     * @RequestMapping(path="/add-watcher/{id}", methods="POST")
     */
    public function testAddWatcher(int $id, RequestInterface $request, ResponseInterface $response)
    {
        try {
            $userId = $request->input('user_id');
            if (!$userId) {
                $userId = getRedmineUserId();
                if (!$userId) {
                    return $this->error('请提供用户ID或确保用户已登录');
                }
            }

            $result = $this->issueLocalApiService->addWatchers($id, (int)$userId);
            
            return $this->success($result, '添加关注人成功');
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('测试添加关注人失败', [
                'issue_id' => $id,
                'user_id' => $request->input('user_id'),
                'error' => $e->getMessage()
            ]);
            
            return $this->error($e->getMessage());
        }
    }

    /**
     * 测试删除关注人
     * 
     * @RequestMapping(path="/del-watcher/{id}", methods="DELETE")
     */
    public function testDelWatcher(int $id, RequestInterface $request, ResponseInterface $response)
    {
        try {
            $userId = $request->input('user_id');
            if (!$userId) {
                $userId = getRedmineUserId();
                if (!$userId) {
                    return $this->error('请提供用户ID或确保用户已登录');
                }
            }

            $result = $this->issueLocalApiService->delWatchers($id, (int)$userId);
            
            return $this->success($result, '删除关注人成功');
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('测试删除关注人失败', [
                'issue_id' => $id,
                'user_id' => $request->input('user_id'),
                'error' => $e->getMessage()
            ]);
            
            return $this->error($e->getMessage());
        }
    }

    /**
     * 运行完整测试套件
     * 
     * @RequestMapping(path="/full-test", methods="POST")
     */
    public function fullTest(RequestInterface $request, ResponseInterface $response)
    {
        $results = [];
        $startTime = microtime(true);
        
        try {
            // 1. 测试创建
            $createParams = [
                'project_id' => $request->input('project_id', 1),
                'tracker_id' => $request->input('tracker_id', 1),
                'subject' => '完整测试 - 本地API创建 ' . date('Y-m-d H:i:s'),
                'description' => '这是完整测试套件创建的Issue',
                'priority_id' => 3,
                'status_id' => 1,
                'start_date' => date('Y-m-d'),
                'due_date' => date('Y-m-d', strtotime('+7 days')),
                'estimated_hours' => 8.0,
                'done_ratio' => 0
            ];
            
            $createResult = $this->issueLocalApiService->createIssue($createParams);
            $results['create'] = [
                'success' => true,
                'data' => $createResult,
                'message' => '创建Issue成功'
            ];
            
            $issueId = $createResult['issue']['id'];
            
            // 2. 测试获取
            $getResult = $this->issueLocalApiService->getIssue($issueId);
            $results['get'] = [
                'success' => true,
                'data' => $getResult,
                'message' => '获取Issue详情成功'
            ];
            
            // 3. 测试更新
            $updateParams = [
                'subject' => '完整测试 - 本地API更新 ' . date('Y-m-d H:i:s'),
                'done_ratio' => 50,
                'notes' => '完整测试套件的更新操作'
            ];
            
            $updateResult = $this->issueLocalApiService->updateIssue($issueId, $updateParams);
            $results['update'] = [
                'success' => true,
                'data' => $updateResult,
                'message' => '更新Issue成功'
            ];
            
            // 4. 测试关注人
            $userId = getRedmineUserId();
            if ($userId) {
                $addWatcherResult = $this->issueLocalApiService->addWatchers($issueId, $userId);
                $delWatcherResult = $this->issueLocalApiService->delWatchers($issueId, $userId);
                
                $results['watcher'] = [
                    'success' => true,
                    'data' => [
                        'add' => $addWatcherResult,
                        'del' => $delWatcherResult
                    ],
                    'message' => '关注人功能测试成功'
                ];
            } else {
                $results['watcher'] = [
                    'success' => false,
                    'message' => '跳过关注人测试 (用户未登录)'
                ];
            }
            
            // 5. 可选删除测试
            if ($request->input('test_delete', false)) {
                $deleteResult = $this->issueLocalApiService->deleteIssue($issueId);
                $results['delete'] = [
                    'success' => true,
                    'data' => $deleteResult,
                    'message' => '删除Issue成功'
                ];
            } else {
                $results['delete'] = [
                    'success' => false,
                    'message' => '跳过删除测试 (保留测试数据)',
                    'issue_id' => $issueId
                ];
            }
            
        } catch (\Exception $e) {
            $results['error'] = [
                'success' => false,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ];
            
            Log::get('system', 'error')->info('完整测试套件失败', [
                'error' => $e->getMessage(),
                'results' => $results
            ]);
        }
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        // 统计结果
        $successCount = 0;
        $totalCount = 0;
        
        foreach ($results as $test => $result) {
            if ($test === 'error') continue;
            
            $totalCount++;
            if ($result['success']) {
                $successCount++;
            }
        }
        
        $successRate = $totalCount > 0 ? round(($successCount / $totalCount) * 100, 1) : 0;
        
        return $this->success([
            'duration' => $duration,
            'success_count' => $successCount,
            'total_count' => $totalCount,
            'success_rate' => $successRate,
            'results' => $results
        ], '完整测试套件执行完成');
    }

    /**
     * 获取测试状态
     * 
     * @RequestMapping(path="/status", methods="GET")
     */
    public function getStatus(RequestInterface $request, ResponseInterface $response)
    {
        try {
            $status = [
                'service_available' => class_exists(IssueLocalApiService::class),
                'database_connection' => $this->checkDatabaseConnection(),
                'user_logged_in' => getRedmineUserId() !== null,
                'current_user_id' => getRedmineUserId(),
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            return $this->success($status, '获取测试状态成功');
            
        } catch (\Exception $e) {
            return $this->error('获取测试状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查数据库连接
     */
    private function checkDatabaseConnection(): bool
    {
        try {
            \Hyperf\DbConnection\Db::connection('tchip_redmine')->select('SELECT 1');
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
