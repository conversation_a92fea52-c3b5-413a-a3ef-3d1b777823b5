<?php
/**
 * Issue本地API实现服务
 * 用于替换Redmine API调用，实现Issue的新建、修改、删除、校验、通知发送等功能
 * 
 * @Copyright T-chip Team.
 * @Date 2025/01/02
 * <AUTHOR> Assistant
 * @Description Issue本地API实现，包含完整的Issue生命周期管理
 */

namespace App\Core\Services\Project;

use App\Constants\IssueCode;
use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\Redmine\AttachmentModel;
use App\Model\Redmine\CustomValuesModel;
use App\Model\Redmine\IssueModel;
use App\Model\Redmine\IssueStatusModel;
use App\Model\Redmine\JournalDetailsModel;
use App\Model\Redmine\JournalsModel;
use App\Model\Redmine\MemberModel;
use App\Model\Redmine\MemberRolesModel;
use App\Model\Redmine\ProjectModel;
use App\Model\Redmine\TrackersModel;
use App\Model\Redmine\UserModel;
use App\Model\Redmine\WatchersModel;
use App\Model\Redmine\WorkflowsModel;
use App\Model\Redmine\IssueAssignedModel;
use App\Model\Redmine\IssuesExtModel;
use Carbon\Carbon;
use Hyperf\Database\Model\Collection;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class IssueLocalApiService extends BusinessService
{
    /**
     * @Inject()
     * @var IssueModel
     */
    protected $issueModel;

    /**
     * 创建Issue
     * 模拟Redmine API的createIssue方法
     * 
     * @param array $params Issue参数
     * @return array 返回创建的Issue信息
     */
    public function createIssue(array $params): array
    {
        Db::connection('tchip_redmine')->beginTransaction();
        try {
            // 权限检查
            $this->validateCreatePermission($params);
            
            // 数据验证
            $this->validateIssueData($params);
            
            // 准备Issue数据
            $issueData = $this->prepareIssueData($params);
            
            // 创建Issue
            $issue = $this->issueModel::create($issueData);
            
            // 创建Journal记录
            $this->createJournal($issue->id, getRedmineUserId(), '');
            
            // 处理自定义字段
            if (!empty($params['custom_fields'])) {
                $this->saveCustomFields($issue->id, $params['custom_fields']);
            }
            
            // 处理关注人
            if (!empty($params['watcher_user_ids'])) {
                $this->saveWatchers($issue->id, $params['watcher_user_ids']);
            }
            
            // 处理多人指派
            if (!empty($params['assigned_to_id']) && is_array($params['assigned_to_id'])) {
                $this->saveMultipleAssignees($issue->id, $params['assigned_to_id']);
            }
            
            // 处理附件
            if (!empty($params['uploads'])) {
                $this->saveAttachments($issue->id, $params['uploads']);
            }
            
            // 发送通知
            $this->sendCreateNotification($issue);
            
            Db::connection('tchip_redmine')->commit();
            
            Log::get('system', 'info')->info('本地API创建Issue成功', [
                'issue_id' => $issue->id,
                'subject' => $issue->subject,
                'project_id' => $issue->project_id
            ]);
            
            return [
                'issue' => [
                    'id' => $issue->id,
                    'subject' => $issue->subject,
                    'project' => ['id' => $issue->project_id],
                    'tracker' => ['id' => $issue->tracker_id],
                    'status' => ['id' => $issue->status_id],
                    'priority' => ['id' => $issue->priority_id],
                    'author' => ['id' => $issue->author_id],
                    'assigned_to' => $issue->assigned_to_id ? ['id' => $issue->assigned_to_id] : null,
                    'created_on' => $issue->created_on,
                    'updated_on' => $issue->updated_on
                ]
            ];
            
        } catch (\Exception $e) {
            Db::connection('tchip_redmine')->rollBack();
            Log::get('system', 'error')->info('本地API创建Issue失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '创建Issue失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新Issue
     * 模拟Redmine API的updateIssue方法
     * 
     * @param int $id Issue ID
     * @param array $params 更新参数
     * @return array 返回更新结果
     */
    public function updateIssue(int $id, array $params): array
    {
        Db::connection('tchip_redmine')->beginTransaction();
        try {
            $issue = $this->issueModel::find($id);
            if (!$issue) {
                throw new AppException(StatusCode::ERR_SERVER, 'Issue不存在');
            }
            
            // 权限检查
            $this->validateUpdatePermission($issue, $params);
            
            // 数据验证
            $this->validateIssueData($params, $issue);
            
            // 记录变更前的状态
            $oldAttributes = $issue->getAttributes();
            
            // 准备更新数据
            $updateData = $this->prepareUpdateData($params, $issue);
            
            // 更新Issue
            $issue->fill($updateData);
            $issue->save();
            
            // 创建Journal记录变更
            $journalId = $this->createJournalWithChanges($issue, $oldAttributes, $params['notes'] ?? '');
            
            // 处理自定义字段变更
            if (isset($params['custom_fields'])) {
                $this->updateCustomFields($issue->id, $params['custom_fields'], $journalId);
            }
            
            // 处理关注人变更
            if (isset($params['watcher_user_ids'])) {
                $this->updateWatchers($issue->id, $params['watcher_user_ids']);
            }
            
            // 处理多人指派变更
            if (isset($params['assigned_to_id']) && is_array($params['assigned_to_id'])) {
                $this->updateMultipleAssignees($issue->id, $params['assigned_to_id']);
            }
            
            // 发送通知
            $this->sendUpdateNotification($issue, $journalId);
            
            Db::connection('tchip_redmine')->commit();
            
            Log::get('system', 'info')->info('本地API更新Issue成功', [
                'issue_id' => $issue->id,
                'changes' => array_keys($updateData)
            ]);
            
            return [
                'success' => true,
                'issue' => [
                    'id' => $issue->id,
                    'updated_on' => $issue->updated_on
                ]
            ];
            
        } catch (\Exception $e) {
            Db::connection('tchip_redmine')->rollBack();
            Log::get('system', 'error')->info('本地API更新Issue失败', [
                'issue_id' => $id,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '更新Issue失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除Issue
     * 模拟Redmine API的deleteIssue方法
     * 
     * @param int $id Issue ID
     * @return array 返回删除结果
     */
    public function deleteIssue(int $id): array
    {
        Db::connection('tchip_redmine')->beginTransaction();
        try {
            $issue = $this->issueModel::find($id);
            if (!$issue) {
                throw new AppException(StatusCode::ERR_SERVER, 'Issue不存在');
            }
            
            // 权限检查
            $this->validateDeletePermission($issue);
            
            // 检查是否有子Issue
            $childIssues = $this->issueModel::where('parent_id', $id)->get();
            if ($childIssues->count() > 0) {
                throw new AppException(StatusCode::ERR_SERVER, '存在子Issue，无法删除');
            }
            
            // 删除相关数据
            $this->deleteRelatedData($issue);
            
            // 删除Issue
            $issue->delete();
            
            Db::connection('tchip_redmine')->commit();
            
            Log::get('system', 'info')->info('本地API删除Issue成功', [
                'issue_id' => $id,
                'subject' => $issue->subject
            ]);
            
            return ['success' => true];
            
        } catch (\Exception $e) {
            Db::connection('tchip_redmine')->rollBack();
            Log::get('system', 'error')->info('本地API删除Issue失败', [
                'issue_id' => $id,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '删除Issue失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取Issue详情
     * 模拟Redmine API的getIssue方法
     * 
     * @param int $id Issue ID
     * @return array 返回Issue详情
     */
    public function getIssue(int $id): array
    {
        $issue = $this->issueModel::with([
            'projectText',
            'tracker',
            'issueStatus',
            'authorText',
            'assignedText',
            'enumeration',
            'attachment',
            'customValues'
        ])->find($id);
        
        if (!$issue) {
            throw new AppException(StatusCode::ERR_SERVER, 'Issue不存在');
        }
        
        // 权限检查
        $this->validateViewPermission($issue);
        
        return [
            'issue' => $this->formatIssueResponse($issue)
        ];
    }

    /**
     * 添加关注人
     * 模拟Redmine API的addWatchers方法
     * 
     * @param int $id Issue ID
     * @param int $userId 用户ID
     * @return array 返回结果
     */
    public function addWatchers(int $id, int $userId): array
    {
        try {
            $issue = $this->issueModel::find($id);
            if (!$issue) {
                throw new AppException(StatusCode::ERR_SERVER, 'Issue不存在');
            }
            
            // 检查是否已经关注
            $exists = WatchersModel::where([
                'watchable_type' => 'Issue',
                'watchable_id' => $id,
                'user_id' => $userId
            ])->exists();
            
            if (!$exists) {
                WatchersModel::create([
                    'watchable_type' => 'Issue',
                    'watchable_id' => $id,
                    'user_id' => $userId
                ]);
            }
            
            return ['success' => true];
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('添加关注人失败', [
                'issue_id' => $id,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '添加关注人失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除关注人
     * 模拟Redmine API的delWatchers方法
     *
     * @param int $id Issue ID
     * @param int $userId 用户ID
     * @return array 返回结果
     */
    public function delWatchers(int $id, int $userId): array
    {
        try {
            WatchersModel::where([
                'watchable_type' => 'Issue',
                'watchable_id' => $id,
                'user_id' => $userId
            ])->delete();

            return ['success' => true];

        } catch (\Exception $e) {
            Log::get('system', 'error')->info('删除关注人失败', [
                'issue_id' => $id,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '删除关注人失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证创建权限
     *
     * @param array $params
     * @throws AppException
     */
    private function validateCreatePermission(array $params): void
    {
        $userId = getRedmineUserId();
        if (!$userId) {
            throw new AppException(StatusCode::ERR_FORBIDDEN, '用户未登录');
        }

        // 检查项目权限
        if (empty($params['project_id'])) {
            throw new AppException(StatusCode::ERR_SERVER, '项目ID不能为空');
        }

        $projectId = $params['project_id'];
        $hasPermission = MemberModel::query()
            ->join('member_roles', 'members.id', '=', 'member_roles.member_id')
            ->where('members.user_id', $userId)
            ->where('members.project_id', $projectId)
            ->exists();

        if (!$hasPermission) {
            throw new AppException(StatusCode::ERR_FORBIDDEN, '没有在该项目中创建Issue的权限');
        }
    }

    /**
     * 验证更新权限
     *
     * @param IssueModel $issue
     * @param array $params
     * @throws AppException
     */
    private function validateUpdatePermission(IssueModel $issue, array $params): void
    {
        $userId = getRedmineUserId();
        if (!$userId) {
            throw new AppException(StatusCode::ERR_FORBIDDEN, '用户未登录');
        }

        // 检查是否是作者或分配人
        $isAuthor = $issue->author_id == $userId;
        $isAssignee = $issue->assigned_to_id == $userId;

        // 检查是否是项目成员
        $isMember = MemberModel::query()
            ->where('user_id', $userId)
            ->where('project_id', $issue->project_id)
            ->exists();

        if (!$isAuthor && !$isAssignee && !$isMember) {
            throw new AppException(StatusCode::ERR_FORBIDDEN, '没有更新该Issue的权限');
        }

        // 检查工作流权限
        if (isset($params['status_id']) && $params['status_id'] != $issue->status_id) {
            $this->validateWorkflowPermission($issue, $params['status_id'], $userId);
        }
    }

    /**
     * 验证删除权限
     *
     * @param IssueModel $issue
     * @throws AppException
     */
    private function validateDeletePermission(IssueModel $issue): void
    {
        $userId = getRedmineUserId();
        if (!$userId) {
            throw new AppException(StatusCode::ERR_FORBIDDEN, '用户未登录');
        }

        // 检查是否是作者
        $isAuthor = $issue->author_id == $userId;

        // 检查是否是项目管理员
        $isManager = MemberModel::query()
            ->join('member_roles', 'members.id', '=', 'member_roles.member_id')
            ->join('roles', 'member_roles.role_id', '=', 'roles.id')
            ->where('members.user_id', $userId)
            ->where('members.project_id', $issue->project_id)
            ->where('roles.name', 'Manager')
            ->exists();

        if (!$isAuthor && !$isManager) {
            throw new AppException(StatusCode::ERR_FORBIDDEN, '只有创建人或项目管理员才能删除Issue');
        }
    }

    /**
     * 验证查看权限
     *
     * @param IssueModel $issue
     * @throws AppException
     */
    private function validateViewPermission(IssueModel $issue): void
    {
        $userId = getRedmineUserId();
        if (!$userId) {
            throw new AppException(StatusCode::ERR_FORBIDDEN, '用户未登录');
        }

        // 检查是否是项目成员
        $isMember = MemberModel::query()
            ->where('user_id', $userId)
            ->where('project_id', $issue->project_id)
            ->exists();

        if (!$isMember) {
            throw new AppException(StatusCode::ERR_FORBIDDEN, '没有查看该Issue的权限');
        }

        // 检查私有Issue权限
        if ($issue->is_private) {
            $isAuthor = $issue->author_id == $userId;
            $isAssignee = $issue->assigned_to_id == $userId;

            if (!$isAuthor && !$isAssignee) {
                throw new AppException(StatusCode::ERR_FORBIDDEN, '没有查看该私有Issue的权限');
            }
        }
    }

    /**
     * 验证工作流权限
     *
     * @param IssueModel $issue
     * @param int $newStatusId
     * @param int $userId
     * @throws AppException
     */
    private function validateWorkflowPermission(IssueModel $issue, int $newStatusId, int $userId): void
    {
        // 获取用户在项目中的角色
        $roles = MemberModel::query()
            ->join('member_roles', 'members.id', '=', 'member_roles.member_id')
            ->where('members.user_id', $userId)
            ->where('members.project_id', $issue->project_id)
            ->pluck('member_roles.role_id')
            ->toArray();

        if (empty($roles)) {
            throw new AppException(StatusCode::ERR_FORBIDDEN, '用户不是项目成员');
        }

        // 检查工作流规则
        $workflowExists = WorkflowsModel::query()
            ->where('tracker_id', $issue->tracker_id)
            ->where('old_status_id', $issue->status_id)
            ->where('new_status_id', $newStatusId)
            ->whereIn('role_id', $roles)
            ->where(function ($query) use ($issue, $userId) {
                $query->where('author', 0)
                      ->orWhere(function ($q) use ($issue, $userId) {
                          $q->where('author', 1)->where($issue->author_id, $userId);
                      });
            })
            ->where(function ($query) use ($issue, $userId) {
                $query->where('assignee', 0)
                      ->orWhere(function ($q) use ($issue, $userId) {
                          $q->where('assignee', 1)->where($issue->assigned_to_id, $userId);
                      });
            })
            ->exists();

        if (!$workflowExists) {
            throw new AppException(StatusCode::ERR_FORBIDDEN, '没有权限将状态从当前状态更改为目标状态');
        }
    }

    /**
     * 验证Issue数据
     *
     * @param array $params
     * @param IssueModel|null $existingIssue
     * @throws AppException
     */
    private function validateIssueData(array $params, ?IssueModel $existingIssue = null): void
    {
        // 验证必填字段
        if (!$existingIssue) {
            // 创建时的必填字段验证
            if (empty($params['subject'])) {
                throw new AppException(StatusCode::ERR_SERVER, '标题不能为空');
            }
            if (empty($params['project_id'])) {
                throw new AppException(StatusCode::ERR_SERVER, '项目ID不能为空');
            }
            if (empty($params['tracker_id'])) {
                throw new AppException(StatusCode::ERR_SERVER, '跟踪器ID不能为空');
            }
        }

        // 验证标题长度
        if (isset($params['subject']) && mb_strlen($params['subject']) > 255) {
            throw new AppException(StatusCode::ERR_SERVER, '标题长度不能超过255个字符');
        }

        // 验证项目是否存在
        if (isset($params['project_id'])) {
            $project = ProjectModel::find($params['project_id']);
            if (!$project) {
                throw new AppException(StatusCode::ERR_SERVER, '项目不存在');
            }
        }

        // 验证跟踪器是否存在
        if (isset($params['tracker_id'])) {
            $tracker = TrackersModel::find($params['tracker_id']);
            if (!$tracker) {
                throw new AppException(StatusCode::ERR_SERVER, '跟踪器不存在');
            }
        }

        // 验证状态是否存在
        if (isset($params['status_id'])) {
            $status = IssueStatusModel::find($params['status_id']);
            if (!$status) {
                throw new AppException(StatusCode::ERR_SERVER, '状态不存在');
            }
        }

        // 验证分配人是否存在
        if (isset($params['assigned_to_id']) && $params['assigned_to_id']) {
            $user = UserModel::find($params['assigned_to_id']);
            if (!$user) {
                throw new AppException(StatusCode::ERR_SERVER, '分配人不存在');
            }
        }

        // 验证日期格式
        if (isset($params['start_date']) && $params['start_date']) {
            if (!$this->isValidDate($params['start_date'])) {
                throw new AppException(StatusCode::ERR_SERVER, '开始日期格式不正确');
            }
        }

        if (isset($params['due_date']) && $params['due_date']) {
            if (!$this->isValidDate($params['due_date'])) {
                throw new AppException(StatusCode::ERR_SERVER, '结束日期格式不正确');
            }
        }

        // 验证日期逻辑
        if (isset($params['start_date']) && isset($params['due_date']) &&
            $params['start_date'] && $params['due_date']) {
            if (strtotime($params['start_date']) > strtotime($params['due_date'])) {
                throw new AppException(StatusCode::ERR_SERVER, '开始日期不能晚于结束日期');
            }
        }

        // 验证完成度
        if (isset($params['done_ratio'])) {
            if (!is_numeric($params['done_ratio']) || $params['done_ratio'] < 0 || $params['done_ratio'] > 100) {
                throw new AppException(StatusCode::ERR_SERVER, '完成度必须是0-100之间的数字');
            }
        }

        // 验证预估工时
        if (isset($params['estimated_hours']) && $params['estimated_hours'] !== null) {
            if (!is_numeric($params['estimated_hours']) || $params['estimated_hours'] < 0) {
                throw new AppException(StatusCode::ERR_SERVER, '预估工时必须是非负数');
            }
        }

        // 验证父Issue
        if (isset($params['parent_issue_id']) && $params['parent_issue_id']) {
            $parentIssue = $this->issueModel::find($params['parent_issue_id']);
            if (!$parentIssue) {
                throw new AppException(StatusCode::ERR_SERVER, '父Issue不存在');
            }

            // 检查循环依赖
            if ($existingIssue && $this->hasCircularDependency($existingIssue->id, $params['parent_issue_id'])) {
                throw new AppException(StatusCode::ERR_SERVER, '不能设置循环依赖的父Issue');
            }
        }
    }

    /**
     * 准备Issue数据
     *
     * @param array $params
     * @return array
     */
    private function prepareIssueData(array $params): array
    {
        $data = [
            'project_id' => $params['project_id'],
            'tracker_id' => $params['tracker_id'],
            'subject' => $params['subject'],
            'description' => $params['description'] ?? '',
            'status_id' => $params['status_id'] ?? IssueCode::ISSUE_STATUS_CREATE_NEW,
            'priority_id' => $params['priority_id'] ?? 3, // 默认优先级
            'author_id' => getRedmineUserId(),
            'created_on' => date('Y-m-d H:i:s'),
            'updated_on' => date('Y-m-d H:i:s'),
        ];

        // 可选字段
        $optionalFields = [
            'assigned_to_id', 'category_id', 'fixed_version_id',
            'start_date', 'due_date', 'estimated_hours', 'done_ratio', 'is_private'
        ];

        foreach ($optionalFields as $field) {
            if (isset($params[$field])) {
                $data[$field] = $params[$field];
            }
        }

        // 处理父Issue
        if (isset($params['parent_issue_id']) && $params['parent_issue_id']) {
            $data['parent_id'] = $params['parent_issue_id'];
        }

        return $data;
    }

    /**
     * 准备更新数据
     *
     * @param array $params
     * @param IssueModel $issue
     * @return array
     */
    private function prepareUpdateData(array $params, IssueModel $issue): array
    {
        $data = ['updated_on' => date('Y-m-d H:i:s')];

        // 可更新字段
        $updatableFields = [
            'subject', 'description', 'status_id', 'priority_id', 'assigned_to_id',
            'category_id', 'fixed_version_id', 'start_date', 'due_date',
            'estimated_hours', 'done_ratio', 'is_private'
        ];

        foreach ($updatableFields as $field) {
            if (array_key_exists($field, $params)) {
                $data[$field] = $params[$field];
            }
        }

        // 处理父Issue
        if (isset($params['parent_issue_id'])) {
            $data['parent_id'] = $params['parent_issue_id'] ?: null;
        }

        // 处理关闭时间
        if (isset($params['status_id'])) {
            $status = IssueStatusModel::find($params['status_id']);
            if ($status && $status->is_closed) {
                $data['closed_on'] = date('Y-m-d H:i:s');
            } elseif (!$status || !$status->is_closed) {
                $data['closed_on'] = null;
            }
        }

        return $data;
    }

    /**
     * 创建Journal记录
     *
     * @param int $issueId
     * @param int $userId
     * @param string $notes
     * @return int Journal ID
     */
    private function createJournal(int $issueId, int $userId, string $notes = ''): int
    {
        $journal = JournalsModel::create([
            'journalized_id' => $issueId,
            'journalized_type' => 'Issue',
            'user_id' => $userId,
            'notes' => $notes,
            'created_on' => date('Y-m-d H:i:s'),
            'private_notes' => 0
        ]);

        return $journal->id;
    }

    /**
     * 创建带变更记录的Journal
     *
     * @param IssueModel $issue
     * @param array $oldAttributes
     * @param string $notes
     * @return int Journal ID
     */
    private function createJournalWithChanges(IssueModel $issue, array $oldAttributes, string $notes = ''): int
    {
        $journalId = $this->createJournal($issue->id, getRedmineUserId(), $notes);

        // 记录字段变更
        $newAttributes = $issue->getAttributes();
        $this->recordAttributeChanges($journalId, $oldAttributes, $newAttributes);

        return $journalId;
    }

    /**
     * 记录属性变更
     *
     * @param int $journalId
     * @param array $oldAttributes
     * @param array $newAttributes
     */
    private function recordAttributeChanges(int $journalId, array $oldAttributes, array $newAttributes): void
    {
        $trackableFields = [
            'subject', 'description', 'status_id', 'priority_id', 'assigned_to_id',
            'category_id', 'fixed_version_id', 'start_date', 'due_date',
            'estimated_hours', 'done_ratio', 'parent_id', 'tracker_id', 'project_id'
        ];

        foreach ($trackableFields as $field) {
            $oldValue = $oldAttributes[$field] ?? null;
            $newValue = $newAttributes[$field] ?? null;

            if ($oldValue != $newValue) {
                JournalDetailsModel::create([
                    'journal_id' => $journalId,
                    'property' => 'attr',
                    'prop_key' => $field,
                    'old_value' => $oldValue,
                    'value' => $newValue
                ]);
            }
        }
    }

    /**
     * 保存自定义字段
     *
     * @param int $issueId
     * @param array $customFields
     */
    private function saveCustomFields(int $issueId, array $customFields): void
    {
        foreach ($customFields as $field) {
            if (isset($field['id']) && isset($field['value'])) {
                CustomValuesModel::updateOrCreate([
                    'customized_type' => 'Issue',
                    'customized_id' => $issueId,
                    'custom_field_id' => $field['id']
                ], [
                    'value' => is_array($field['value']) ? implode(',', $field['value']) : $field['value']
                ]);
            }
        }
    }

    /**
     * 更新自定义字段
     *
     * @param int $issueId
     * @param array $customFields
     * @param int $journalId
     */
    private function updateCustomFields(int $issueId, array $customFields, int $journalId): void
    {
        foreach ($customFields as $field) {
            if (isset($field['id'])) {
                $customFieldId = $field['id'];
                $newValue = isset($field['value']) ?
                    (is_array($field['value']) ? implode(',', $field['value']) : $field['value']) : '';

                // 获取旧值
                $oldCustomValue = CustomValuesModel::where([
                    'customized_type' => 'Issue',
                    'customized_id' => $issueId,
                    'custom_field_id' => $customFieldId
                ])->first();

                $oldValue = $oldCustomValue ? $oldCustomValue->value : '';

                // 更新或创建自定义字段值
                CustomValuesModel::updateOrCreate([
                    'customized_type' => 'Issue',
                    'customized_id' => $issueId,
                    'custom_field_id' => $customFieldId
                ], [
                    'value' => $newValue
                ]);

                // 记录变更
                if ($oldValue != $newValue) {
                    JournalDetailsModel::create([
                        'journal_id' => $journalId,
                        'property' => 'cf',
                        'prop_key' => $customFieldId,
                        'old_value' => $oldValue,
                        'value' => $newValue
                    ]);
                }
            }
        }
    }

    /**
     * 保存关注人
     *
     * @param int $issueId
     * @param array $userIds
     */
    private function saveWatchers(int $issueId, array $userIds): void
    {
        foreach ($userIds as $userId) {
            WatchersModel::firstOrCreate([
                'watchable_type' => 'Issue',
                'watchable_id' => $issueId,
                'user_id' => $userId
            ]);
        }
    }

    /**
     * 更新关注人
     *
     * @param int $issueId
     * @param array $userIds
     */
    private function updateWatchers(int $issueId, array $userIds): void
    {
        // 删除现有关注人
        WatchersModel::where([
            'watchable_type' => 'Issue',
            'watchable_id' => $issueId
        ])->delete();

        // 添加新的关注人
        $this->saveWatchers($issueId, $userIds);
    }

    /**
     * 保存多人指派
     *
     * @param int $issueId
     * @param array $userIds
     */
    private function saveMultipleAssignees(int $issueId, array $userIds): void
    {
        foreach ($userIds as $userId) {
            IssueAssignedModel::firstOrCreate([
                'issue_id' => $issueId,
                'user_id' => $userId
            ], [
                'leader' => 0
            ]);
        }
    }

    /**
     * 更新多人指派
     *
     * @param int $issueId
     * @param array $userIds
     */
    private function updateMultipleAssignees(int $issueId, array $userIds): void
    {
        // 软删除现有指派
        IssueAssignedModel::where('issue_id', $issueId)->delete();

        // 添加新的指派
        $this->saveMultipleAssignees($issueId, $userIds);
    }

    /**
     * 保存附件
     *
     * @param int $issueId
     * @param array $uploads
     */
    private function saveAttachments(int $issueId, array $uploads): void
    {
        foreach ($uploads as $upload) {
            if (isset($upload['token']) && isset($upload['filename'])) {
                AttachmentModel::create([
                    'container_id' => $issueId,
                    'container_type' => 'Issue',
                    'filename' => $upload['filename'],
                    'disk_filename' => $upload['token'],
                    'filesize' => $upload['filesize'] ?? 0,
                    'content_type' => $upload['content_type'] ?? 'application/octet-stream',
                    'digest' => $upload['digest'] ?? '',
                    'downloads' => 0,
                    'author_id' => getRedmineUserId(),
                    'created_on' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }

    /**
     * 发送创建通知
     *
     * @param IssueModel $issue
     */
    private function sendCreateNotification(IssueModel $issue): void
    {
        try {
            // 获取通知用户列表
            $notifiedUsers = $this->getNotifiedUsers($issue);

            if (!empty($notifiedUsers)) {
                // 这里可以集成邮件发送服务
                Log::get('system', 'info')->info('Issue创建通知', [
                    'issue_id' => $issue->id,
                    'notified_users' => $notifiedUsers,
                    'subject' => $issue->subject
                ]);

                // TODO: 实际的邮件发送逻辑
                // make(MailService::class)->sendIssueCreateNotification($issue, $notifiedUsers);
            }
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('发送创建通知失败', [
                'issue_id' => $issue->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发送更新通知
     *
     * @param IssueModel $issue
     * @param int $journalId
     */
    private function sendUpdateNotification(IssueModel $issue, int $journalId): void
    {
        try {
            // 获取通知用户列表
            $notifiedUsers = $this->getNotifiedUsers($issue);

            if (!empty($notifiedUsers)) {
                Log::get('system', 'info')->info('Issue更新通知', [
                    'issue_id' => $issue->id,
                    'journal_id' => $journalId,
                    'notified_users' => $notifiedUsers,
                    'subject' => $issue->subject
                ]);

                // TODO: 实际的邮件发送逻辑
                // make(MailService::class)->sendIssueUpdateNotification($issue, $journalId, $notifiedUsers);
            }
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('发送更新通知失败', [
                'issue_id' => $issue->id,
                'journal_id' => $journalId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取通知用户列表
     *
     * @param IssueModel $issue
     * @return array
     */
    private function getNotifiedUsers(IssueModel $issue): array
    {
        $users = [];

        // 作者
        if ($issue->author_id) {
            $users[] = $issue->author_id;
        }

        // 分配人
        if ($issue->assigned_to_id) {
            $users[] = $issue->assigned_to_id;
        }

        // 关注人
        $watchers = WatchersModel::where([
            'watchable_type' => 'Issue',
            'watchable_id' => $issue->id
        ])->pluck('user_id')->toArray();

        $users = array_merge($users, $watchers);

        // 多人指派
        $assignedUsers = IssueAssignedModel::where('issue_id', $issue->id)
            ->pluck('user_id')->toArray();

        $users = array_merge($users, $assignedUsers);

        // 去重并过滤无效用户
        $users = array_unique($users);
        $users = array_filter($users);

        return $users;
    }

    /**
     * 删除相关数据
     *
     * @param IssueModel $issue
     */
    private function deleteRelatedData(IssueModel $issue): void
    {
        // 删除Journal记录
        $journalIds = JournalsModel::where([
            'journalized_type' => 'Issue',
            'journalized_id' => $issue->id
        ])->pluck('id')->toArray();

        if (!empty($journalIds)) {
            JournalDetailsModel::whereIn('journal_id', $journalIds)->delete();
            JournalsModel::whereIn('id', $journalIds)->delete();
        }

        // 删除自定义字段值
        CustomValuesModel::where([
            'customized_type' => 'Issue',
            'customized_id' => $issue->id
        ])->delete();

        // 删除关注人
        WatchersModel::where([
            'watchable_type' => 'Issue',
            'watchable_id' => $issue->id
        ])->delete();

        // 删除多人指派
        IssueAssignedModel::where('issue_id', $issue->id)->delete();

        // 删除扩展信息
        IssuesExtModel::where('issue_id', $issue->id)->delete();

        // 删除附件（可选，根据业务需求）
        // AttachmentModel::where([
        //     'container_type' => 'Issue',
        //     'container_id' => $issue->id
        // ])->delete();
    }

    /**
     * 格式化Issue响应
     *
     * @param IssueModel $issue
     * @return array
     */
    private function formatIssueResponse(IssueModel $issue): array
    {
        return [
            'id' => $issue->id,
            'subject' => $issue->subject,
            'description' => $issue->description,
            'project' => $issue->projectText ? [
                'id' => $issue->projectText->id,
                'name' => $issue->projectText->name
            ] : null,
            'tracker' => $issue->tracker ? [
                'id' => $issue->tracker->id,
                'name' => $issue->tracker->name
            ] : null,
            'status' => $issue->issueStatus ? [
                'id' => $issue->issueStatus->id,
                'name' => $issue->issueStatus->name
            ] : null,
            'priority' => $issue->enumeration ? [
                'id' => $issue->enumeration->id,
                'name' => $issue->enumeration->name
            ] : null,
            'author' => $issue->authorText ? [
                'id' => $issue->authorText->id,
                'name' => $issue->authorText->firstname . ' ' . $issue->authorText->lastname
            ] : null,
            'assigned_to' => $issue->assignedText ? [
                'id' => $issue->assignedText->id,
                'name' => $issue->assignedText->firstname . ' ' . $issue->assignedText->lastname
            ] : null,
            'created_on' => $issue->created_on,
            'updated_on' => $issue->updated_on,
            'start_date' => $issue->start_date,
            'due_date' => $issue->due_date,
            'done_ratio' => $issue->done_ratio,
            'estimated_hours' => $issue->estimated_hours,
            'custom_fields' => $this->formatCustomFields($issue->customValues),
            'attachments' => $this->formatAttachments($issue->attachment)
        ];
    }

    /**
     * 格式化自定义字段
     *
     * @param Collection|null $customValues
     * @return array
     */
    private function formatCustomFields(?Collection $customValues): array
    {
        if (!$customValues) {
            return [];
        }

        $result = [];
        foreach ($customValues as $value) {
            $result[] = [
                'id' => $value->custom_field_id,
                'name' => $value->field['name'] ?? '',
                'value' => $value->value
            ];
        }

        return $result;
    }

    /**
     * 格式化附件
     *
     * @param Collection|null $attachments
     * @return array
     */
    private function formatAttachments(?Collection $attachments): array
    {
        if (!$attachments) {
            return [];
        }

        $result = [];
        foreach ($attachments as $attachment) {
            $result[] = [
                'id' => $attachment->id,
                'filename' => $attachment->filename,
                'filesize' => $attachment->filesize,
                'content_type' => $attachment->content_type,
                'created_on' => $attachment->created_on
            ];
        }

        return $result;
    }

    /**
     * 检查日期格式是否有效
     *
     * @param string $date
     * @return bool
     */
    private function isValidDate(string $date): bool
    {
        $formats = ['Y-m-d', 'Y-m-d H:i:s'];

        foreach ($formats as $format) {
            $d = \DateTime::createFromFormat($format, $date);
            if ($d && $d->format($format) === $date) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查循环依赖
     *
     * @param int $issueId
     * @param int $parentId
     * @return bool
     */
    private function hasCircularDependency(int $issueId, int $parentId): bool
    {
        if ($issueId === $parentId) {
            return true;
        }

        $parent = $this->issueModel::find($parentId);
        if (!$parent || !$parent->parent_id) {
            return false;
        }

        return $this->hasCircularDependency($issueId, $parent->parent_id);
    }

    /**
     * 添加Issue关联
     * 模拟Redmine API的addIssueRelation方法
     *
     * @param int $id Issue ID
     * @param array $params 关联参数
     * @return array 返回结果
     */
    public function addIssueRelation(int $id, array $params): array
    {
        try {
            // 验证Issue是否存在
            $issue = $this->issueModel::find($id);
            if (!$issue) {
                throw new AppException(StatusCode::ERR_SERVER, 'Issue不存在');
            }

            // 验证目标Issue是否存在
            if (empty($params['issue_to_id'])) {
                throw new AppException(StatusCode::ERR_SERVER, '目标Issue ID不能为空');
            }

            $targetIssue = $this->issueModel::find($params['issue_to_id']);
            if (!$targetIssue) {
                throw new AppException(StatusCode::ERR_SERVER, '目标Issue不存在');
            }

            // 创建关联关系
            $relationData = [
                'issue_from_id' => $id,
                'issue_to_id' => $params['issue_to_id'],
                'relation_type' => $params['relation_type'] ?? 'relates',
                'delay' => $params['delay'] ?? null
            ];

            $relation = \App\Model\Redmine\IssueRelationModel::create($relationData);

            return [
                'relation' => [
                    'id' => $relation->id,
                    'issue_from_id' => $relation->issue_from_id,
                    'issue_to_id' => $relation->issue_to_id,
                    'relation_type' => $relation->relation_type,
                    'delay' => $relation->delay
                ]
            ];

        } catch (\Exception $e) {
            Log::get('system', 'error')->info('添加Issue关联失败', [
                'issue_id' => $id,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '添加Issue关联失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除Issue关联
     * 模拟Redmine API的delIssueRelation方法
     *
     * @param int $relationId 关联ID
     * @return array 返回结果
     */
    public function delIssueRelation(int $relationId): array
    {
        try {
            $relation = \App\Model\Redmine\IssueRelationModel::find($relationId);
            if (!$relation) {
                throw new AppException(StatusCode::ERR_SERVER, 'Issue关联不存在');
            }

            $relation->delete();

            return ['success' => true];

        } catch (\Exception $e) {
            Log::get('system', 'error')->info('删除Issue关联失败', [
                'relation_id' => $relationId,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '删除Issue关联失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量操作Issue
     *
     * @param array $issueIds Issue ID数组
     * @param array $params 更新参数
     * @return array 返回结果
     */
    public function batchUpdateIssues(array $issueIds, array $params): array
    {
        $results = [];
        $errors = [];

        foreach ($issueIds as $issueId) {
            try {
                $result = $this->updateIssue($issueId, $params);
                $results[] = [
                    'issue_id' => $issueId,
                    'success' => true,
                    'result' => $result
                ];
            } catch (\Exception $e) {
                $errors[] = [
                    'issue_id' => $issueId,
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'success_count' => count($results),
            'error_count' => count($errors),
            'results' => $results,
            'errors' => $errors
        ];
    }
}
