<?php
/**
 * Issue本地API实现服务
 * 用于替换Redmine API调用，直接操作数据库
 * 
 * @Copyright T-chip Team.
 * @Date 2025-01-02
 * <AUTHOR> Assistant
 * @Description 实现Issue的CRUD操作，包括Journal日志、关联关系等
 */

namespace App\Core\Services\Project;

use App\Constants\IssueCode;
use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\Redmine\AttachmentModel;
use App\Model\Redmine\CustomValuesModel;
use App\Model\Redmine\IssueModel;
use App\Model\Redmine\IssueRelationModel;
use App\Model\Redmine\IssuesExtModel;
use App\Model\Redmine\JournalDetailsModel;
use App\Model\Redmine\JournalsModel;
use App\Model\Redmine\WatchersModel;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class IssueLocalApiService extends BusinessService
{
    /**
     * @Inject()
     * @var IssueModel
     */
    protected $issueModel;

    /**
     * 创建Issue
     * 模拟Redmine API的createIssue方法
     * 
     * @param array $params Issue参数
     * @return array 返回创建的Issue信息
     */
    public function createIssue(array $params): array
    {
        DB::connection('tchip_redmine')->beginTransaction();
        try {
            // 准备Issue数据
            $issueData = $this->prepareIssueData($params);
            
            // 创建Issue
            $issue = $this->issueModel::create($issueData);
            
            // 创建Journal记录
            $this->createJournal($issue->id, getRedmineUserId(), '');
            
            // 处理自定义字段
            if (!empty($params['custom_fields'])) {
                $this->saveCustomFields($issue->id, $params['custom_fields']);
            }
            
            // 处理关注人
            if (!empty($params['watcher_user_ids'])) {
                $this->saveWatchers($issue->id, $params['watcher_user_ids']);
            }
            
            // 更新嵌套集合属性
            $this->updateNestedSetAttributes($issue);
            
            DB::connection('tchip_redmine')->commit();
            
            Log::get('system', 'error')->info('本地API创建Issue成功', [
                'issue_id' => $issue->id,
                'subject' => $issue->subject
            ]);
            
            return [
                'issue' => [
                    'id' => $issue->id,
                    'subject' => $issue->subject,
                    'project' => ['id' => $issue->project_id],
                    'tracker' => ['id' => $issue->tracker_id],
                    'status' => ['id' => $issue->status_id],
                    'priority' => ['id' => $issue->priority_id],
                    'author' => ['id' => $issue->author_id],
                    'assigned_to' => $issue->assigned_to_id ? ['id' => $issue->assigned_to_id] : null,
                    'created_on' => $issue->created_on,
                    'updated_on' => $issue->updated_on
                ]
            ];
            
        } catch (\Exception $e) {
            DB::connection('tchip_redmine')->rollBack();
            Log::get('system', 'error')->info('本地API创建Issue失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '创建Issue失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新Issue
     * 模拟Redmine API的updateIssue方法
     * 
     * @param int $id Issue ID
     * @param array $params 更新参数
     * @return array 返回更新结果
     */
    public function updateIssue(int $id, array $params): array
    {
        DB::connection('tchip_redmine')->beginTransaction();
        try {
            $issue = $this->issueModel::find($id);
            if (!$issue) {
                throw new AppException(StatusCode::ERR_SERVER, 'Issue不存在');
            }
            
            // 记录变更前的状态
            $oldAttributes = $issue->getAttributes();
            
            // 准备更新数据
            $updateData = $this->prepareIssueData($params, false);
            
            // 更新Issue
            $issue->update($updateData);
            
            // 创建Journal记录变更
            $journalId = $this->createJournal($issue->id, getRedmineUserId(), $params['notes'] ?? '');
            
            // 记录字段变更
            $this->recordFieldChanges($journalId, $oldAttributes, $issue->fresh()->getAttributes());
            
            // 处理自定义字段更新
            if (isset($params['custom_fields'])) {
                $this->updateCustomFields($issue->id, $params['custom_fields'], $journalId);
            }
            
            // 处理关注人更新
            if (isset($params['watcher_user_ids'])) {
                $this->updateWatchers($issue->id, $params['watcher_user_ids']);
            }
            
            // 更新嵌套集合属性
            $this->updateNestedSetAttributes($issue);
            
            DB::connection('tchip_redmine')->commit();
            
            Log::get('system', 'error')->info('本地API更新Issue成功', [
                'issue_id' => $id,
                'changes' => array_keys($updateData)
            ]);
            
            return ['success' => true];
            
        } catch (\Exception $e) {
            DB::connection('tchip_redmine')->rollBack();
            Log::get('system', 'error')->info('本地API更新Issue失败', [
                'issue_id' => $id,
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '更新Issue失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除Issue
     * 模拟Redmine API的deleteIssue方法
     * 
     * @param int $id Issue ID
     * @return array 返回删除结果
     */
    public function deleteIssue(int $id): array
    {
        DB::connection('tchip_redmine')->beginTransaction();
        try {
            $issue = $this->issueModel::find($id);
            if (!$issue) {
                throw new AppException(StatusCode::ERR_SERVER, 'Issue不存在');
            }
            
            // 删除相关数据
            $this->deleteRelatedData($id);
            
            // 删除Issue本身
            $issue->delete();
            
            DB::connection('tchip_redmine')->commit();
            
            Log::get('system', 'error')->info('本地API删除Issue成功', [
                'issue_id' => $id
            ]);
            
            return ['success' => true];
            
        } catch (\Exception $e) {
            DB::connection('tchip_redmine')->rollBack();
            Log::get('system', 'error')->info('本地API删除Issue失败', [
                'issue_id' => $id,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '删除Issue失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取Issue详情
     * 模拟Redmine API的getIssue方法
     * 
     * @param int $id Issue ID
     * @return array Issue详情
     */
    public function getIssue(int $id): array
    {
        try {
            $issue = $this->issueModel::with([
                'attachment',
                'customValues',
                'watcher'
            ])->find($id);
            
            if (!$issue) {
                throw new AppException(StatusCode::ERR_SERVER, 'Issue不存在');
            }
            
            // 获取Journal记录
            $journals = JournalsModel::with(['details', 'userInfo'])
                ->where('journalized_id', $id)
                ->where('journalized_type', 'Issue')
                ->orderBy('created_on', 'asc')
                ->get();
            
            return [
                'issue' => [
                    'id' => $issue->id,
                    'subject' => $issue->subject,
                    'description' => $issue->description,
                    'project' => ['id' => $issue->project_id],
                    'tracker' => ['id' => $issue->tracker_id],
                    'status' => ['id' => $issue->status_id],
                    'priority' => ['id' => $issue->priority_id],
                    'author' => ['id' => $issue->author_id],
                    'assigned_to' => $issue->assigned_to_id ? ['id' => $issue->assigned_to_id] : null,
                    'created_on' => $issue->created_on,
                    'updated_on' => $issue->updated_on,
                    'start_date' => $issue->start_date,
                    'due_date' => $issue->due_date,
                    'done_ratio' => $issue->done_ratio,
                    'estimated_hours' => $issue->estimated_hours,
                    'parent' => $issue->parent_id ? ['id' => $issue->parent_id] : null,
                    'attachments' => $issue->attachment->toArray(),
                    'custom_fields' => $this->formatCustomFields($issue->customValues),
                    'watchers' => $issue->watcher->pluck('user_id')->toArray(),
                    'journals' => $this->formatJournals($journals)
                ]
            ];
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('本地API获取Issue失败', [
                'issue_id' => $id,
                'error' => $e->getMessage()
            ]);
            throw new AppException(StatusCode::ERR_SERVER, '获取Issue失败: ' . $e->getMessage());
        }
    }
