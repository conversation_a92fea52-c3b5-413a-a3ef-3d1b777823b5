# Issue本地API实现

## 概述

本项目实现了一个完整的Issue本地API服务，用于替换原有的Redmine API调用。该实现包含了Issue的新建、修改、删除、校验、通知发送等各种核心逻辑，完全模拟了Redmine的Issue管理功能。

## 文件结构

```
app/Core/Services/Project/
├── IssueLocalApiService.php     # 本地API实现主文件
├── IssueLocalApiTest.php        # 测试类
├── IssueService.php             # 原有服务类（已修改为使用本地API）
└── IssueLocalApi_README.md      # 本文档
```

## 主要功能

### 1. Issue生命周期管理

#### 创建Issue
- **方法**: `createIssue(array $params): array`
- **功能**: 创建新的Issue，包含完整的数据验证和权限检查
- **特性**:
  - 权限验证（项目成员权限）
  - 数据完整性验证
  - 自动创建Journal记录
  - 处理自定义字段
  - 处理关注人和多人指派
  - 发送创建通知

#### 更新Issue
- **方法**: `updateIssue(int $id, array $params): array`
- **功能**: 更新现有Issue
- **特性**:
  - 权限验证（作者、分配人、项目成员）
  - 工作流权限检查
  - 变更记录追踪
  - Journal详细记录
  - 发送更新通知

#### 删除Issue
- **方法**: `deleteIssue(int $id): array`
- **功能**: 删除Issue及相关数据
- **特性**:
  - 权限验证（作者或项目管理员）
  - 检查子Issue依赖
  - 级联删除相关数据
  - 完整的数据清理

#### 获取Issue详情
- **方法**: `getIssue(int $id): array`
- **功能**: 获取Issue完整信息
- **特性**:
  - 权限验证
  - 私有Issue权限检查
  - 关联数据加载
  - 格式化输出

### 2. 关注人管理

#### 添加关注人
- **方法**: `addWatchers(int $id, int $userId): array`
- **功能**: 为Issue添加关注人

#### 删除关注人
- **方法**: `delWatchers(int $id, int $userId): array`
- **功能**: 删除Issue关注人

### 3. Issue关联管理

#### 添加Issue关联
- **方法**: `addIssueRelation(int $id, array $params): array`
- **功能**: 创建Issue之间的关联关系

#### 删除Issue关联
- **方法**: `delIssueRelation(int $relationId): array`
- **功能**: 删除Issue关联关系

### 4. 批量操作

#### 批量更新Issue
- **方法**: `batchUpdateIssues(array $issueIds, array $params): array`
- **功能**: 批量更新多个Issue

## 核心特性

### 1. 完整的权限控制

#### 创建权限
- 检查用户是否为项目成员
- 验证项目访问权限

#### 更新权限
- 作者权限检查
- 分配人权限检查
- 项目成员权限检查
- 工作流状态转换权限验证

#### 删除权限
- 作者权限检查
- 项目管理员权限检查

#### 查看权限
- 项目成员权限检查
- 私有Issue权限验证

### 2. 数据验证

#### 基础验证
- 必填字段验证
- 字段长度验证
- 数据类型验证
- 外键关联验证

#### 业务逻辑验证
- 日期逻辑验证（开始日期不能晚于结束日期）
- 完成度范围验证（0-100）
- 预估工时非负验证
- 循环依赖检查

### 3. Journal变更追踪

#### 自动记录变更
- 字段级变更追踪
- 自定义字段变更记录
- 附件变更记录
- 关联关系变更记录

#### 变更详情
- 记录变更前后值
- 支持变更备注
- 用户和时间戳记录

### 4. 通知系统

#### 通知用户确定
- Issue作者
- Issue分配人
- 关注人
- 多人指派用户

#### 通知事件
- Issue创建通知
- Issue更新通知
- 状态变更通知
- 分配人变更通知

### 5. 数据完整性

#### 事务管理
- 所有操作使用数据库事务
- 异常时自动回滚
- 确保数据一致性

#### 关联数据处理
- 自定义字段同步
- 关注人数据同步
- 多人指派数据同步
- 附件数据处理

## 使用方法

### 1. 基本使用

```php
// 注入服务
use App\Core\Services\Project\IssueLocalApiService;

/**
 * @Inject()
 * @var IssueLocalApiService
 */
protected $issueLocalApiService;

// 创建Issue
$params = [
    'project_id' => 1,
    'tracker_id' => 1,
    'subject' => 'Issue标题',
    'description' => 'Issue描述',
    'priority_id' => 3,
    'status_id' => 1
];
$result = $this->issueLocalApiService->createIssue($params);

// 更新Issue
$updateParams = [
    'subject' => '更新后的标题',
    'status_id' => 2,
    'notes' => '更新备注'
];
$result = $this->issueLocalApiService->updateIssue($issueId, $updateParams);
```

### 2. 在IssueService中的集成

原有的IssueService已经修改为使用本地API实现：

```php
// 原来的调用
$this->redmineIssueService->createIssue($params);

// 现在的调用
$this->issueLocalApiService->createIssue($params);
```

### 3. 测试使用

```php
use App\Core\Services\Project\IssueLocalApiTest;

$testService = make(IssueLocalApiTest::class);

// 运行完整测试套件
$results = $testService->runFullTest();

// 单独测试创建功能
$createResult = $testService->testCreateIssue();
```

## 配置说明

### 1. 数据库连接

本地API使用`tchip_redmine`数据库连接，确保配置正确：

```php
// 在代码中使用
Db::connection('tchip_redmine')->beginTransaction();
```

### 2. 日志配置

使用系统日志记录操作：

```php
Log::get('system', 'info')->info('操作成功', $data);
Log::get('system', 'error')->info('操作失败', $error);
```

## 注意事项

### 1. 兼容性
- 完全兼容原有Redmine API接口
- 返回数据格式保持一致
- 支持所有原有功能

### 2. 性能
- 直接数据库操作，性能更优
- 减少HTTP请求开销
- 支持事务处理

### 3. 扩展性
- 模块化设计，易于扩展
- 支持自定义验证规则
- 支持自定义通知逻辑

### 4. 安全性
- 完整的权限验证
- SQL注入防护
- 数据验证和过滤

## 故障排除

### 1. 权限问题
- 检查用户是否为项目成员
- 验证工作流配置
- 确认角色权限设置

### 2. 数据验证失败
- 检查必填字段
- 验证数据格式
- 确认外键关联

### 3. 事务失败
- 检查数据库连接
- 查看错误日志
- 验证数据完整性

## 更新日志

### v1.0.0 (2025-01-02)
- 初始版本发布
- 实现完整的Issue CRUD操作
- 支持权限验证和数据验证
- 集成Journal变更追踪
- 实现通知系统
- 添加测试套件
