<?php
/**
 * Issue本地API测试类
 * 用于测试本地API实现的功能
 * 
 * @Copyright T-chip Team.
 * @Date 2025/01/02
 * <AUTHOR> Assistant
 * @Description Issue本地API测试
 */

namespace App\Core\Services\Project;

use App\Core\Services\BusinessService;
use App\Core\Utils\Log;
use Hyperf\Di\Annotation\Inject;

class IssueLocalApiTest extends BusinessService
{
    /**
     * @Inject()
     * @var IssueLocalApiService
     */
    protected $issueLocalApiService;

    /**
     * 测试创建Issue
     */
    public function testCreateIssue(): array
    {
        try {
            $params = [
                'project_id' => 1, // 请根据实际项目ID调整
                'tracker_id' => 1, // 请根据实际跟踪器ID调整
                'subject' => '测试Issue - 本地API创建',
                'description' => '这是通过本地API创建的测试Issue',
                'priority_id' => 3,
                'status_id' => 1,
                'start_date' => date('Y-m-d'),
                'due_date' => date('Y-m-d', strtotime('+7 days')),
                'estimated_hours' => 8.0,
                'done_ratio' => 0
            ];

            $result = $this->issueLocalApiService->createIssue($params);
            
            Log::get('system', 'info')->info('测试创建Issue成功', $result);
            
            return [
                'success' => true,
                'message' => '创建Issue测试成功',
                'data' => $result
            ];
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('测试创建Issue失败', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '创建Issue测试失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 测试更新Issue
     */
    public function testUpdateIssue(int $issueId): array
    {
        try {
            $params = [
                'subject' => '测试Issue - 本地API更新',
                'description' => '这是通过本地API更新的测试Issue',
                'status_id' => 2,
                'done_ratio' => 50,
                'notes' => '通过本地API进行的更新测试'
            ];

            $result = $this->issueLocalApiService->updateIssue($issueId, $params);
            
            Log::get('system', 'info')->info('测试更新Issue成功', $result);
            
            return [
                'success' => true,
                'message' => '更新Issue测试成功',
                'data' => $result
            ];
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('测试更新Issue失败', [
                'issue_id' => $issueId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '更新Issue测试失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 测试获取Issue详情
     */
    public function testGetIssue(int $issueId): array
    {
        try {
            $result = $this->issueLocalApiService->getIssue($issueId);
            
            Log::get('system', 'info')->info('测试获取Issue详情成功', [
                'issue_id' => $issueId,
                'subject' => $result['issue']['subject'] ?? ''
            ]);
            
            return [
                'success' => true,
                'message' => '获取Issue详情测试成功',
                'data' => $result
            ];
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('测试获取Issue详情失败', [
                'issue_id' => $issueId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '获取Issue详情测试失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 测试添加关注人
     */
    public function testAddWatcher(int $issueId, int $userId): array
    {
        try {
            $result = $this->issueLocalApiService->addWatchers($issueId, $userId);
            
            Log::get('system', 'info')->info('测试添加关注人成功', [
                'issue_id' => $issueId,
                'user_id' => $userId
            ]);
            
            return [
                'success' => true,
                'message' => '添加关注人测试成功',
                'data' => $result
            ];
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('测试添加关注人失败', [
                'issue_id' => $issueId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '添加关注人测试失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 测试删除关注人
     */
    public function testDelWatcher(int $issueId, int $userId): array
    {
        try {
            $result = $this->issueLocalApiService->delWatchers($issueId, $userId);
            
            Log::get('system', 'info')->info('测试删除关注人成功', [
                'issue_id' => $issueId,
                'user_id' => $userId
            ]);
            
            return [
                'success' => true,
                'message' => '删除关注人测试成功',
                'data' => $result
            ];
            
        } catch (\Exception $e) {
            Log::get('system', 'error')->info('测试删除关注人失败', [
                'issue_id' => $issueId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '删除关注人测试失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 运行完整测试套件
     */
    public function runFullTest(): array
    {
        $results = [];
        
        // 测试创建Issue
        $createResult = $this->testCreateIssue();
        $results['create'] = $createResult;
        
        if ($createResult['success']) {
            $issueId = $createResult['data']['issue']['id'];
            
            // 测试获取Issue详情
            $results['get'] = $this->testGetIssue($issueId);
            
            // 测试更新Issue
            $results['update'] = $this->testUpdateIssue($issueId);
            
            // 测试关注人功能
            $userId = getRedmineUserId();
            if ($userId) {
                $results['add_watcher'] = $this->testAddWatcher($issueId, $userId);
                $results['del_watcher'] = $this->testDelWatcher($issueId, $userId);
            }
        }
        
        // 统计测试结果
        $totalTests = count($results);
        $successTests = count(array_filter($results, function($result) {
            return $result['success'];
        }));
        
        return [
            'total_tests' => $totalTests,
            'success_tests' => $successTests,
            'failed_tests' => $totalTests - $successTests,
            'success_rate' => $totalTests > 0 ? round(($successTests / $totalTests) * 100, 2) : 0,
            'results' => $results
        ];
    }
}
