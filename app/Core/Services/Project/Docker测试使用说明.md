# Issue本地API Docker环境测试使用说明

## 概述

本文档说明如何在docker-compose环境中测试Issue本地API实现。我们提供了两种测试方式：
1. **命令行测试** - 通过Hyperf命令行工具
2. **HTTP接口测试** - 通过REST API接口

## 环境准备

### 1. 确保Docker容器运行

```bash
# 进入docker-compose目录
cd docker_compose

# 启动容器
docker-compose up -d

# 查看容器状态
docker-compose ps
```

### 2. 进入容器

```bash
# 进入容器（容器名称根据您的配置可能不同）
docker exec -it dev-tchip-bi bash

# 或者使用docker-compose
docker-compose exec tchip-bi bash
```

## 方式一：命令行测试

### 基本用法

```bash
# 在容器内执行以下命令

# 1. 运行完整测试套件（推荐）
php bin/hyperf.php issue:test-local-api full

# 2. 单独测试创建功能
php bin/hyperf.php issue:test-local-api create --project-id=1 --tracker-id=1

# 3. 单独测试更新功能（需要先有Issue ID）
php bin/hyperf.php issue:test-local-api update --issue-id=123

# 4. 单独测试获取功能
php bin/hyperf.php issue:test-local-api get --issue-id=123

# 5. 单独测试关注人功能
php bin/hyperf.php issue:test-local-api watcher --issue-id=123 --user-id=1

# 6. 单独测试删除功能（谨慎使用）
php bin/hyperf.php issue:test-local-api delete --issue-id=123
```

### 参数说明

- `--project-id` : 项目ID（默认为1）
- `--tracker-id` : 跟踪器ID（默认为1）
- `--issue-id` : Issue ID（用于更新、获取、删除操作）
- `--user-id` : 用户ID（用于关注人操作）

### 示例输出

```bash
root@container:/var/www# php bin/hyperf.php issue:test-local-api full

开始测试Issue本地API实现...
测试动作: full
运行完整测试套件...
测试创建Issue...
✓ 创建Issue成功
Issue ID: 12345
标题: 测试Issue - 本地API创建 2025-01-02 15:30:45
测试获取Issue详情 (ID: 12345)...
✓ 获取Issue详情成功
标题: 测试Issue - 本地API创建 2025-01-02 15:30:45
状态: 新建
创建时间: 2025-01-02 15:30:45
更新时间: 2025-01-02 15:30:45
测试更新Issue (ID: 12345)...
✓ 更新Issue成功
更新时间: 2025-01-02 15:30:46
测试关注人功能 (Issue ID: 12345, User ID: 1)...
✓ 添加关注人成功
✓ 删除关注人成功
是否测试删除功能？ (yes/no) [no]:
> no

=== 测试结果汇总 ===
测试耗时: 2.35 秒
✓ create: 成功
✓ get: 成功
✓ update: 成功
✓ watcher: 成功
✗ delete: 跳过或失败
成功率: 4/5 (80.0%)
🎉 测试通过！本地API实现工作正常
```

## 方式二：HTTP接口测试

### 基本接口

所有接口都在 `/api/test/issue-local-api` 路径下：

```bash
# 基础URL（根据您的配置调整端口）
BASE_URL="http://localhost:8057/api/test/issue-local-api"
```

### 1. 检查测试状态

```bash
curl -X GET "${BASE_URL}/status"
```

### 2. 创建Issue

```bash
curl -X POST "${BASE_URL}/create" \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": 1,
    "tracker_id": 1,
    "subject": "API测试Issue",
    "description": "通过HTTP接口创建的测试Issue",
    "priority_id": 3,
    "status_id": 1
  }'
```

### 3. 获取Issue详情

```bash
# 替换123为实际的Issue ID
curl -X GET "${BASE_URL}/get/123"
```

### 4. 更新Issue

```bash
# 替换123为实际的Issue ID
curl -X PUT "${BASE_URL}/update/123" \
  -H "Content-Type: application/json" \
  -d '{
    "subject": "更新后的标题",
    "done_ratio": 50,
    "notes": "通过API更新"
  }'
```

### 5. 添加关注人

```bash
# 替换123为实际的Issue ID
curl -X POST "${BASE_URL}/add-watcher/123" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1
  }'
```

### 6. 删除关注人

```bash
# 替换123为实际的Issue ID
curl -X DELETE "${BASE_URL}/del-watcher/123" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1
  }'
```

### 7. 运行完整测试套件

```bash
curl -X POST "${BASE_URL}/full-test" \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": 1,
    "tracker_id": 1,
    "test_delete": false
  }'
```

### 8. 删除Issue（谨慎使用）

```bash
# 替换123为实际的Issue ID
curl -X DELETE "${BASE_URL}/delete/123"
```

## 使用Postman测试

您也可以导入以下Postman集合进行测试：

### 环境变量
- `base_url`: `http://localhost:8057/api/test/issue-local-api`
- `issue_id`: 从创建接口获取的Issue ID

### 测试流程
1. 调用 `GET /status` 检查状态
2. 调用 `POST /create` 创建Issue
3. 从响应中获取Issue ID
4. 调用其他接口进行测试

## 故障排除

### 1. 权限问题

如果遇到权限错误：

```bash
# 检查用户是否登录
curl -X GET "${BASE_URL}/status"

# 查看日志
docker-compose logs tchip-bi | grep -i error
```

### 2. 数据库连接问题

```bash
# 在容器内检查数据库连接
php bin/hyperf.php

# 检查数据库配置
cat config/autoload/databases.php
```

### 3. 服务未启动

```bash
# 重启容器
docker-compose restart tchip-bi

# 查看容器日志
docker-compose logs -f tchip-bi
```

### 4. 查看详细日志

```bash
# 在容器内查看日志
tail -f runtime/logs/hyperf.log

# 查看错误日志
tail -f runtime/logs/hyperf-error.log
```

## 测试数据清理

### 清理测试Issue

如果需要清理测试创建的Issue：

```bash
# 方法1：通过API删除（如果知道Issue ID）
curl -X DELETE "${BASE_URL}/delete/{issue_id}"

# 方法2：直接在数据库中清理（谨慎使用）
# 进入容器后连接数据库
mysql -h数据库主机 -u用户名 -p数据库名
DELETE FROM issues WHERE subject LIKE '%测试Issue%' AND subject LIKE '%本地API%';
```

## 性能测试

### 批量测试

```bash
# 创建多个Issue进行性能测试
for i in {1..10}; do
  curl -X POST "${BASE_URL}/create" \
    -H "Content-Type: application/json" \
    -d "{
      \"project_id\": 1,
      \"tracker_id\": 1,
      \"subject\": \"性能测试Issue ${i}\",
      \"description\": \"批量创建测试 ${i}\"
    }"
  echo "Created issue $i"
done
```

## 注意事项

1. **测试环境**: 请确保在测试环境中运行，避免影响生产数据
2. **权限配置**: 确保测试用户有足够的权限进行Issue操作
3. **数据备份**: 在进行删除测试前，建议备份重要数据
4. **日志监控**: 测试过程中注意监控日志，及时发现问题
5. **资源清理**: 测试完成后及时清理测试数据

## 常见问题

### Q: 命令找不到
A: 确保在容器内执行命令，并且路径正确

### Q: 数据库连接失败
A: 检查数据库配置和网络连接

### Q: 权限被拒绝
A: 确保用户已登录且有相应权限

### Q: Issue创建失败
A: 检查项目ID和跟踪器ID是否存在

### Q: 测试数据过多
A: 定期清理测试数据，避免影响系统性能
