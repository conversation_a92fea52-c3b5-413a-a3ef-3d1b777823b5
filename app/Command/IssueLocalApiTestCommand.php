<?php

declare(strict_types=1);

namespace App\Command;

use App\Core\Services\Project\IssueLocalApiService;
use App\Core\Utils\Log;
use Hyperf\Command\Command as HyperfCommand;
use Hyperf\Command\Annotation\Command;
use Hyperf\Di\Annotation\Inject;
use Psr\Container\ContainerInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;

/**
 * Issue本地API测试命令
 * 
 * @Command
 */
class IssueLocalApiTestCommand extends HyperfCommand
{
    /**
     * 执行的命令行
     *
     * @var string
     */
    protected $name = 'issue:test-local-api';

    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @Inject()
     * @var IssueLocalApiService
     */
    protected $issueLocalApiService;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
        parent::__construct('issue:test-local-api');
    }

    public function configure()
    {
        parent::configure();
        $this->setDescription('测试Issue本地API实现');
        $this->addArgument('action', InputArgument::OPTIONAL, '测试动作: create|update|get|delete|watcher|full', 'full');
        $this->addOption('issue-id', 'i', InputOption::VALUE_OPTIONAL, 'Issue ID (用于update/get/delete/watcher操作)');
        $this->addOption('user-id', 'u', InputOption::VALUE_OPTIONAL, '用户ID (用于watcher操作)');
        $this->addOption('project-id', 'p', InputOption::VALUE_OPTIONAL, '项目ID (用于create操作)', '1');
        $this->addOption('tracker-id', 't', InputOption::VALUE_OPTIONAL, '跟踪器ID (用于create操作)', '1');
    }

    public function handle()
    {
        $action = $this->input->getArgument('action');
        
        $this->line("开始测试Issue本地API实现...", 'info');
        $this->line("测试动作: {$action}", 'info');
        
        try {
            switch ($action) {
                case 'create':
                    $this->testCreate();
                    break;
                case 'update':
                    $this->testUpdate();
                    break;
                case 'get':
                    $this->testGet();
                    break;
                case 'delete':
                    $this->testDelete();
                    break;
                case 'watcher':
                    $this->testWatcher();
                    break;
                case 'full':
                    $this->testFull();
                    break;
                default:
                    $this->error("未知的测试动作: {$action}");
                    $this->line("可用动作: create|update|get|delete|watcher|full");
                    return;
            }
        } catch (\Exception $e) {
            $this->error("测试失败: " . $e->getMessage());
            Log::get('system', 'error')->info('Issue本地API测试失败', [
                'action' => $action,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 测试创建Issue
     */
    private function testCreate(): void
    {
        $this->line("测试创建Issue...", 'comment');
        
        $projectId = $this->input->getOption('project-id');
        $trackerId = $this->input->getOption('tracker-id');
        
        $params = [
            'project_id' => (int)$projectId,
            'tracker_id' => (int)$trackerId,
            'subject' => '测试Issue - 本地API创建 ' . date('Y-m-d H:i:s'),
            'description' => '这是通过本地API创建的测试Issue，创建时间：' . date('Y-m-d H:i:s'),
            'priority_id' => 3,
            'status_id' => 1,
            'start_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d', strtotime('+7 days')),
            'estimated_hours' => 8.0,
            'done_ratio' => 0
        ];

        $result = $this->issueLocalApiService->createIssue($params);
        
        $this->line("✓ 创建Issue成功", 'info');
        $this->line("Issue ID: " . $result['issue']['id'], 'info');
        $this->line("标题: " . $result['issue']['subject'], 'info');
        
        // 保存Issue ID供后续测试使用
        $this->saveTestIssueId($result['issue']['id']);
    }

    /**
     * 测试更新Issue
     */
    private function testUpdate(): void
    {
        $issueId = $this->getIssueId();
        if (!$issueId) return;
        
        $this->line("测试更新Issue (ID: {$issueId})...", 'comment');
        
        $params = [
            'subject' => '测试Issue - 本地API更新 ' . date('Y-m-d H:i:s'),
            'description' => '这是通过本地API更新的测试Issue，更新时间：' . date('Y-m-d H:i:s'),
            'done_ratio' => 50,
            'notes' => '通过本地API进行的更新测试 - ' . date('Y-m-d H:i:s')
        ];

        $result = $this->issueLocalApiService->updateIssue($issueId, $params);
        
        $this->line("✓ 更新Issue成功", 'info');
        $this->line("更新时间: " . $result['issue']['updated_on'], 'info');
    }

    /**
     * 测试获取Issue详情
     */
    private function testGet(): void
    {
        $issueId = $this->getIssueId();
        if (!$issueId) return;
        
        $this->line("测试获取Issue详情 (ID: {$issueId})...", 'comment');
        
        $result = $this->issueLocalApiService->getIssue($issueId);
        
        $this->line("✓ 获取Issue详情成功", 'info');
        $this->line("标题: " . $result['issue']['subject'], 'info');
        $this->line("状态: " . ($result['issue']['status']['name'] ?? '未知'), 'info');
        $this->line("创建时间: " . $result['issue']['created_on'], 'info');
        $this->line("更新时间: " . $result['issue']['updated_on'], 'info');
    }

    /**
     * 测试删除Issue
     */
    private function testDelete(): void
    {
        $issueId = $this->getIssueId();
        if (!$issueId) return;
        
        $this->line("测试删除Issue (ID: {$issueId})...", 'comment');
        
        if (!$this->confirm("确定要删除Issue #{$issueId} 吗？")) {
            $this->line("取消删除操作", 'comment');
            return;
        }
        
        $result = $this->issueLocalApiService->deleteIssue($issueId);
        
        $this->line("✓ 删除Issue成功", 'info');
        
        // 清除保存的Issue ID
        $this->clearTestIssueId();
    }

    /**
     * 测试关注人功能
     */
    private function testWatcher(): void
    {
        $issueId = $this->getIssueId();
        if (!$issueId) return;
        
        $userId = $this->input->getOption('user-id');
        if (!$userId) {
            // 尝试获取当前用户ID
            $userId = getRedmineUserId();
            if (!$userId) {
                $this->error("请提供用户ID (--user-id) 或确保用户已登录");
                return;
            }
        }
        
        $this->line("测试关注人功能 (Issue ID: {$issueId}, User ID: {$userId})...", 'comment');
        
        // 测试添加关注人
        $result = $this->issueLocalApiService->addWatchers($issueId, (int)$userId);
        $this->line("✓ 添加关注人成功", 'info');
        
        // 测试删除关注人
        $result = $this->issueLocalApiService->delWatchers($issueId, (int)$userId);
        $this->line("✓ 删除关注人成功", 'info');
    }

    /**
     * 完整测试套件
     */
    private function testFull(): void
    {
        $this->line("运行完整测试套件...", 'comment');
        
        $results = [];
        $startTime = microtime(true);
        
        try {
            // 1. 测试创建
            $this->testCreate();
            $results['create'] = true;
            
            // 2. 测试获取
            $this->testGet();
            $results['get'] = true;
            
            // 3. 测试更新
            $this->testUpdate();
            $results['update'] = true;
            
            // 4. 测试关注人
            $userId = getRedmineUserId();
            if ($userId) {
                $this->testWatcher();
                $results['watcher'] = true;
            } else {
                $this->line("跳过关注人测试 (用户未登录)", 'comment');
                $results['watcher'] = false;
            }
            
            // 5. 测试删除 (可选)
            if ($this->confirm("是否测试删除功能？")) {
                $this->testDelete();
                $results['delete'] = true;
            } else {
                $results['delete'] = false;
            }
            
        } catch (\Exception $e) {
            $this->error("测试过程中出现错误: " . $e->getMessage());
            $results['error'] = $e->getMessage();
        }
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        // 输出测试结果
        $this->line("", 'info');
        $this->line("=== 测试结果汇总 ===", 'info');
        $this->line("测试耗时: {$duration} 秒", 'info');
        
        $successCount = 0;
        $totalCount = 0;
        
        foreach ($results as $test => $success) {
            if ($test === 'error') continue;
            
            $totalCount++;
            if ($success) {
                $successCount++;
                $this->line("✓ {$test}: 成功", 'info');
            } else {
                $this->line("✗ {$test}: 跳过或失败", 'comment');
            }
        }
        
        if (isset($results['error'])) {
            $this->line("✗ 错误: " . $results['error'], 'error');
        }
        
        $successRate = $totalCount > 0 ? round(($successCount / $totalCount) * 100, 1) : 0;
        $this->line("成功率: {$successCount}/{$totalCount} ({$successRate}%)", 'info');
        
        if ($successRate >= 80) {
            $this->line("🎉 测试通过！本地API实现工作正常", 'info');
        } else {
            $this->line("⚠️  部分测试失败，请检查日志", 'comment');
        }
    }

    /**
     * 获取Issue ID
     */
    private function getIssueId(): ?int
    {
        $issueId = $this->input->getOption('issue-id');
        
        if (!$issueId) {
            // 尝试从临时文件读取
            $issueId = $this->loadTestIssueId();
        }
        
        if (!$issueId) {
            $this->error("请提供Issue ID (--issue-id) 或先运行创建测试");
            return null;
        }
        
        return (int)$issueId;
    }

    /**
     * 保存测试Issue ID到临时文件
     */
    private function saveTestIssueId(int $issueId): void
    {
        $tempFile = runtime_path('temp/test_issue_id.txt');
        $dir = dirname($tempFile);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        file_put_contents($tempFile, $issueId);
    }

    /**
     * 从临时文件加载测试Issue ID
     */
    private function loadTestIssueId(): ?int
    {
        $tempFile = runtime_path('temp/test_issue_id.txt');
        if (file_exists($tempFile)) {
            $issueId = (int)file_get_contents($tempFile);
            return $issueId > 0 ? $issueId : null;
        }
        return null;
    }

    /**
     * 清除测试Issue ID
     */
    private function clearTestIssueId(): void
    {
        $tempFile = runtime_path('temp/test_issue_id.txt');
        if (file_exists($tempFile)) {
            unlink($tempFile);
        }
    }
}
